<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                // Test the test endpoint first
                const testResponse = await fetch('http://localhost:3000/api/test');
                const testData = await testResponse.json();
                console.log('Test endpoint response:', testData);
                
                // Test the analyze-website endpoint with a simple URL
                const analyzeResponse = await fetch('http://localhost:3000/api/analyze-website?url=https://example.com');
                const analyzeData = await analyzeResponse.json();
                console.log('Analyze endpoint response:', analyzeData);
                
                resultDiv.innerHTML = `
                    <h3>Test Results:</h3>
                    <p><strong>Test endpoint:</strong> ${JSON.stringify(testData)}</p>
                    <p><strong>Analyze endpoint:</strong> ${JSON.stringify(analyzeData)}</p>
                `;
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
