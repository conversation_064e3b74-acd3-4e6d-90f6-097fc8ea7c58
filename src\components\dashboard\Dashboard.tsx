import { useState } from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { ArticleManagement } from './ArticleManagement';
import { TopicsView } from './TopicsView';
import { SettingsView } from './SettingsView';
import { SocialMediaView } from './SocialMediaView';
import { WriteOptionsModal } from './WriteOptionsModal';
import { UpgradeModal } from './UpgradeModal';
import { WritePage } from './WritePage';

export const Dashboard = () => {
  const [activeView, setActiveView] = useState('articles');
  const [showWriteOptions, setShowWriteOptions] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [isWriting, setIsWriting] = useState(false);

  const handleWrite = () => {
    setShowWriteOptions(true);
  };

  const handleGenerateWithAI = () => {
    setShowWriteOptions(false);
    setShowUpgradeModal(true);
  };

  const handleWriteManually = () => {
    setShowWriteOptions(false);
    setIsWriting(true);
  };

  const handleSelectPlan = () => {
    setShowUpgradeModal(false);
    // Handle plan selection
  };

  const handleBackToDashboard = () => {
    setIsWriting(false);
  };

  if (isWriting) {
    return <WritePage onBackToDashboard={handleBackToDashboard} />;
  }

  const renderContent = () => {
    switch (activeView) {
      case 'articles':
        return <ArticleManagement />;
      case 'topics':
        return <TopicsView />;
      case 'settings':
        return <SettingsView />;
      case 'social':
        return <SocialMediaView />;
      default:
        return <ArticleManagement />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar
        activeView={activeView}
        onViewChange={setActiveView}
        onWrite={handleWrite}
      />
      <div className="flex-1 flex flex-col">
        <Header />
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>

      {showWriteOptions && (
        <WriteOptionsModal
          onClose={() => setShowWriteOptions(false)}
          onGenerateWithAI={handleGenerateWithAI}
          onWriteManually={handleWriteManually}
        />
      )}

      {showUpgradeModal && (
        <UpgradeModal
          onClose={() => setShowUpgradeModal(false)}
          onSelectPlan={handleSelectPlan}
        />
      )}
    </div>
  );
};
