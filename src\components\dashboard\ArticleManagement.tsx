import { useState } from 'react';
import { ArticleEditor } from './ArticleEditor';
import { ArticlePreview } from './ArticlePreview';
import { ArticleList } from './ArticleList';

interface Article {
  id: number;
  title: string;
  description: string;
  content: string;
  tag: string;
  status: string;
  author: string;
  date: string;
  image: string;
}

export const ArticleManagement = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [editingArticle, setEditingArticle] = useState<Article | null>(null);
  const [previewingArticle, setPreviewingArticle] = useState<Article | null>(null);

  const tabs = [
    { id: 'all', label: 'All', count: 2 },
    { id: 'draft', label: 'Draft', count: 0 },
    { id: 'scheduled', label: 'Scheduled', count: 0 },
    { id: 'generated', label: 'Generated', count: 2 },
    { id: 'published', label: 'Published', count: 0 }
  ];

  const articles = [
    {
      id: 1,
      title: "Top Strategies to Win AI Quiz Games and Maximize Rewards",
      description: "Elevate your performance in AI-driven quiz challenges. Discover key methods for better outcomes, smart agent use, and effective clock handling to boost yo...",
      content: "<h1>Top Strategies to Win AI Quiz Games and Maximize Rewards</h1><p>In the rapidly evolving world of AI-driven quiz games, success requires more than just knowledge – it demands strategy, timing, and understanding of how AI operates. This comprehensive guide will walk you through proven techniques to enhance your performance and maximize your rewards.</p><h2>1. Understanding AI Quiz Mechanics</h2><p>Before diving into strategies, it's crucial to understand how AI quiz games work:</p><ul><li>Pattern Recognition: AI uses algorithms to generate questions</li><li>Adaptive Difficulty: Questions may adjust based on your performance</li><li>Time Management: Speed often affects scoring</li></ul>",
      author: "<EMAIL>",
      date: "31 May 2025",
      status: "Generated",
      image: "/lovable-uploads/04065baa-c663-4e7e-bc70-b9708730a5fb.png",
      tag: "AI Quiz Games"
    },
    {
      id: 2,
      title: "Your Smart Guide to India's Festive Online Sale Season",
      description: "Discover smart tips and strategies to find the best online offers and save substantially during India's vibrant festive sale season...",
      content: "<h1>Your Smart Guide to India's Festive Online Sale Season</h1><p>In India, festivals are far more than just holidays; they are deeply intertwined with the tradition of shopping. It's a period when households across the country buzz with plans for new purchases, and e-commerce platforms often report a significant surge in activity.</p>",
      author: "<EMAIL>",
      date: "2 Jun 2025",
      status: "Generated",
      image: "/lovable-uploads/festive-sale.png",
      tag: "Online Shopping"
    }
  ];

  const handleEdit = (id: number) => {
    const articleToEdit = articles.find(article => article.id === id);
    if (articleToEdit) {
      setEditingArticle(articleToEdit);
    }
  };

  const handlePreview = (id: number) => {
    const articleToPreview = articles.find(article => article.id === id);
    if (articleToPreview) {
      setPreviewingArticle(articleToPreview);
    }
  };

  const handleDelete = (id: number) => {
    console.log('Delete article:', id);
  };

  const handleSaveArticle = (updatedArticle: Article) => {
    console.log('Save updated article:', updatedArticle);
    setEditingArticle(null);
  };

  const handleCreateArticle = () => {
    console.log('Create new article');
  };

  const filteredArticles = articles.filter(article => {
    if (activeTab === 'all') return true;
    return article.status.toLowerCase() === activeTab;
  });

  const getEmptyStateMessage = () => {
    switch (activeTab) {
      case 'draft':
        return 'No draft articles';
      case 'scheduled':
        return 'No scheduled articles';
      case 'generated':
        return 'No generated articles';
      case 'published':
        return 'No published articles';
      default:
        return 'No articles yet';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${activeTab === tab.id
              ? 'border-orange-500 text-orange-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
          >
            {tab.label}
            <span className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded-full">
              {tab.count}
            </span>
          </button>
        ))}
      </div>

      <ArticleList
        articles={filteredArticles}
        onEdit={handleEdit}
        onPreview={handlePreview}
        onDelete={handleDelete}
        emptyStateMessage={getEmptyStateMessage()}
        emptyStateAction={handleCreateArticle}
      />

      <ArticleEditor
        article={editingArticle}
        onClose={() => setEditingArticle(null)}
        onSave={handleSaveArticle}
      />

      <ArticlePreview
        article={previewingArticle}
        onClose={() => setPreviewingArticle(null)}
      />
    </div>
  );
};
