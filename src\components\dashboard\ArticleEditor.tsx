import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import Link from '@tiptap/extension-link';

interface Article {
    id: number;
    title: string;
    description: string;
    content: string;
    tag: string;
    status: string;
    author: string;
    date: string;
    image: string;
    urlSlug?: string;
    metaDescription?: string;
    excerpt?: string;
    keywords?: string[];
}

interface ArticleEditorProps {
    article: Article | null;
    onClose: () => void;
    onSave: (article: Article) => void;
}

export const ArticleEditor = ({ article, onClose, onSave }: ArticleEditorProps) => {
    const [editedArticle, setEditedArticle] = useState<Article>({
        id: 0,
        title: '',
        description: '',
        content: '',
        tag: '',
        status: '',
        author: '',
        date: '',
        image: '',
        urlSlug: '',
        metaDescription: '',
        excerpt: '',
        keywords: [],
    });

    const editor = useEditor({
        extensions: [
            StarterKit,
            Underline,
            TextStyle,
            Color,
            Link.configure({
                openOnClick: false,
            }),
        ],
        content: editedArticle.content,
        onUpdate: ({ editor }) => {
            setEditedArticle(prev => ({ ...prev, content: editor.getHTML() }));
        },
    });

    useEffect(() => {
        if (article) {
            setEditedArticle({
                ...article,
                urlSlug: article.urlSlug || '',
                metaDescription: article.metaDescription || '',
                excerpt: article.excerpt || '',
                keywords: article.keywords || [],
            });
            editor?.commands.setContent(article.content);
        }
    }, [article, editor]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSave(editedArticle);
    };

    if (!article) return null;

    return (
        <div className="fixed inset-0 bg-white z-50 flex flex-col h-screen">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b">
                <button
                    onClick={onClose}
                    className="text-gray-600 hover:text-gray-800 flex items-center gap-2"
                >
                    ← Back to Dashboard
                </button>

                <div className="flex items-center gap-2">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={onClose}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        className="bg-orange-500 hover:bg-orange-600 text-white"
                    >
                        Save Changes
                    </Button>
                </div>
            </div>

            <div className="flex h-[calc(100vh-80px)]">
                {/* Main Content Area */}
                <div className="flex-1 overflow-y-auto p-6 space-y-6">
                    <div>
                        <h2 className="text-lg font-medium mb-4">Title & intro</h2>
                        <div className="space-y-4">
                            <Input
                                placeholder="Blog title (mandatory)"
                                value={editedArticle.title}
                                onChange={(e) => setEditedArticle({ ...editedArticle, title: e.target.value })}
                            />
                            <Textarea
                                placeholder="Article intro (optional)"
                                value={editedArticle.description}
                                onChange={(e) => setEditedArticle({ ...editedArticle, description: e.target.value })}
                                className="h-32"
                            />
                        </div>
                    </div>

                    <div className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-lg font-medium">Content</h2>
                            <span className="text-sm text-gray-500">Maximum size 5 MB</span>
                        </div>

                        <div className="border rounded-md">
                            <div className="border-b p-2 flex flex-wrap gap-2">
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().toggleBold().run()}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('bold') ? 'bg-gray-200' : ''}`}
                                    title="Bold"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path><path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path></svg>
                                </button>
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().toggleItalic().run()}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('italic') ? 'bg-gray-200' : ''}`}
                                    title="Italic"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="19" y1="4" x2="10" y2="4"></line><line x1="14" y1="20" x2="5" y2="20"></line><line x1="15" y1="4" x2="9" y2="20"></line></svg>
                                </button>
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().toggleUnderline().run()}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('underline') ? 'bg-gray-200' : ''}`}
                                    title="Underline"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path><line x1="4" y1="21" x2="20" y2="21"></line></svg>
                                </button>
                                <div className="w-px h-6 bg-gray-200 mx-1"></div>
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('heading', { level: 1 }) ? 'bg-gray-200' : ''}`}
                                    title="Heading 1"
                                >
                                    H1
                                </button>
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}`}
                                    title="Heading 2"
                                >
                                    H2
                                </button>
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('heading', { level: 3 }) ? 'bg-gray-200' : ''}`}
                                    title="Heading 3"
                                >
                                    H3
                                </button>
                                <div className="w-px h-6 bg-gray-200 mx-1"></div>
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().toggleBulletList().run()}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('bulletList') ? 'bg-gray-200' : ''}`}
                                    title="Bullet List"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="8" y1="6" x2="21" y2="6"></line><line x1="8" y1="12" x2="21" y2="12"></line><line x1="8" y1="18" x2="21" y2="18"></line><line x1="3" y1="6" x2="3.01" y2="6"></line><line x1="3" y1="12" x2="3.01" y2="12"></line><line x1="3" y1="18" x2="3.01" y2="18"></line></svg>
                                </button>
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().toggleOrderedList().run()}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('orderedList') ? 'bg-gray-200' : ''}`}
                                    title="Numbered List"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="10" y1="6" x2="21" y2="6"></line><line x1="10" y1="12" x2="21" y2="12"></line><line x1="10" y1="18" x2="21" y2="18"></line><path d="M4 6h1v4"></path><path d="M4 10h2"></path><path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"></path></svg>
                                </button>
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().toggleBlockquote().run()}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('blockquote') ? 'bg-gray-200' : ''}`}
                                    title="Quote"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path><path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path></svg>
                                </button>
                                <div className="w-px h-6 bg-gray-200 mx-1"></div>
                                <button
                                    type="button"
                                    onClick={() => {
                                        const url = window.prompt('Enter the URL:');
                                        if (url) {
                                            editor?.chain().focus().setLink({ href: url }).run();
                                        }
                                    }}
                                    className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('link') ? 'bg-gray-200' : ''}`}
                                    title="Add Link"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg>
                                </button>
                                <button
                                    type="button"
                                    onClick={() => editor?.chain().focus().unsetLink().run()}
                                    className={`p-2 rounded hover:bg-gray-100`}
                                    title="Remove Link"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18.36 6.64a9 9 0 1 1-12.73 0"></path><line x1="12" y1="2" x2="12" y2="12"></line></svg>
                                </button>
                            </div>
                            <EditorContent
                                editor={editor}
                                className="min-h-[400px] prose max-w-none focus:outline-none p-4"
                            />
                        </div>
                    </div>
                </div>

                {/* Right Sidebar */}
                <div className="w-80 border-l overflow-y-auto p-6 space-y-6 bg-gray-50">
                    <div>
                        <h2 className="text-lg font-medium mb-4">Info</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Status
                                </label>
                                <Input
                                    value={editedArticle.status}
                                    onChange={(e) => setEditedArticle({ ...editedArticle, status: e.target.value })}
                                    placeholder="Enter status"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Keywords
                                </label>
                                <Input
                                    placeholder="Add keywords to track"
                                    value={editedArticle.keywords?.join(', ') || ''}
                                    onChange={(e) => setEditedArticle({ ...editedArticle, keywords: e.target.value.split(', ').filter(Boolean) })}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Tags
                                </label>
                                <Input
                                    placeholder="Add tags"
                                    value={editedArticle.tag}
                                    onChange={(e) => setEditedArticle({ ...editedArticle, tag: e.target.value })}
                                />
                            </div>
                        </div>
                    </div>

                    <div>
                        <h2 className="text-lg font-medium mb-4">Article Content</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    URL Slug
                                </label>
                                <Input
                                    placeholder="URL Slug"
                                    value={editedArticle.urlSlug}
                                    onChange={(e) => setEditedArticle({ ...editedArticle, urlSlug: e.target.value })}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Meta description
                                </label>
                                <Textarea
                                    placeholder="Add meta-description here"
                                    value={editedArticle.metaDescription}
                                    onChange={(e) => setEditedArticle({ ...editedArticle, metaDescription: e.target.value })}
                                    className="h-32"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Excerpt
                                </label>
                                <Textarea
                                    placeholder="Enter your excerpt here"
                                    value={editedArticle.excerpt}
                                    onChange={(e) => setEditedArticle({ ...editedArticle, excerpt: e.target.value })}
                                    className="h-32"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}; 