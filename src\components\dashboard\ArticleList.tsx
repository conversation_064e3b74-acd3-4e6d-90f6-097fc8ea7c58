import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Article {
    id: number;
    title: string;
    description: string;
    content: string;
    tag: string;
    status: string;
    author: string;
    date: string;
    image: string;
}

interface ArticleListProps {
    articles: Article[];
    onEdit: (id: number) => void;
    onPreview: (id: number) => void;
    onDelete: (id: number) => void;
    emptyStateMessage?: string;
    emptyStateAction?: () => void;
}

export const ArticleList = ({
    articles,
    onEdit,
    onPreview,
    onDelete,
    emptyStateMessage = "No articles yet",
    emptyStateAction
}: ArticleListProps) => {
    return (
        <div className="space-y-6">
            {articles.length > 0 ? (
                <>
                    <div className="flex justify-between items-center">
                        <div className="flex space-x-4 text-sm text-gray-600">
                            <span>📄 Post</span>
                            <span>👤 Author</span>
                            <span>📅 Created on</span>
                            <span>🔄 Status</span>
                        </div>
                    </div>

                    <div className="space-y-4">
                        {articles.map((article) => (
                            <Card key={article.id} className="p-4 hover:shadow-md transition-shadow">
                                <div className="flex items-start space-x-4">
                                    <div className="w-16 h-16 bg-gradient-to-br from-blue-900 to-purple-900 rounded-lg flex items-center justify-center">
                                        <span className="text-white text-2xl">🧠</span>
                                    </div>

                                    <div className="flex-1">
                                        <div className="flex items-start justify-between">
                                            <div>
                                                <h3 className="font-semibold text-gray-900 mb-1">
                                                    {article.title}
                                                </h3>
                                                <p className="text-sm text-gray-600 mb-2">
                                                    {article.description}
                                                </p>
                                                <Badge className="bg-orange-100 text-orange-700 text-xs">
                                                    {article.tag}
                                                </Badge>
                                            </div>

                                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                                                <span>{article.author}</span>
                                                <span>{article.date}</span>
                                                <Badge
                                                    variant="secondary"
                                                    className={`${getStatusColor(article.status)}`}
                                                >
                                                    • {article.status}
                                                </Badge>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger className="text-gray-400 hover:text-gray-600">
                                                        ⋯
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem onClick={() => onEdit(article.id)}>
                                                            ✏️ Edit
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => onPreview(article.id)}>
                                                            👁️ Preview
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            onClick={() => onDelete(article.id)}
                                                            className="text-red-600"
                                                        >
                                                            🗑️ Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        ))}
                    </div>
                </>
            ) : (
                <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-2xl">📄</span>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{emptyStateMessage}</h3>
                    <p className="text-gray-600 mb-4">Start creating your first SEO-optimized article</p>
                    {emptyStateAction && (
                        <Button
                            onClick={emptyStateAction}
                            className="bg-orange-500 hover:bg-orange-600 text-white"
                        >
                            Create Article
                        </Button>
                    )}
                </div>
            )}
        </div>
    );
};

const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
        case 'draft':
            return 'bg-gray-100 text-gray-700';
        case 'scheduled':
            return 'bg-blue-100 text-blue-700';
        case 'generated':
            return 'bg-green-100 text-green-700';
        case 'published':
            return 'bg-purple-100 text-purple-700';
        default:
            return 'bg-gray-100 text-gray-700';
    }
}; 