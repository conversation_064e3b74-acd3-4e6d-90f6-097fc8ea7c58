import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { WebsiteInfo } from '@/lib/services/websiteService';
import { GeneratedArticle } from '@/lib/services/articleService';

interface TopicGenerationProps {
  onNext: () => void;
  websiteInfo: WebsiteInfo | null;
  article: GeneratedArticle | null;
}

export const TopicGeneration = ({ onNext, websiteInfo, article }: TopicGenerationProps) => {
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const suggestedTopics = [
    {
      title: "Top Strategies to Win AI Quiz Games and Maximize Rewards",
      category: "AI Quiz Games",
      keywords: "AI quiz games, winning quiz strategies, quiz game tips, improve quiz..."
    },
    {
      title: "How Blockchain Rewards Are Revolutionizing Online Gaming",
      category: "Blockchain Rewards",
      keywords: "blockchain rewards, gaming with blockchain, digital token rewards, blockchain..."
    },
    {
      title: "Interactive Learning Through AI-Powered Quiz Contests",
      category: "Interactive Learning",
      keywords: "interactive learning, AI-powered quizzes, gamified education, learning through..."
    }
  ];

  const handleTopicSelect = (title: string) => {
    setSelectedTopics(prev =>
      prev.includes(title)
        ? prev.filter(t => t !== title)
        : [...prev, title]
    );
  };

  const handleContinue = () => {
    setIsLoading(true);
    setTimeout(() => {
      onNext();
    }, 1000);
  };

  if (!websiteInfo || !article) {
    return (
      <div className="w-full max-w-4xl mx-auto p-8 text-center">
        <p className="text-red-500">Error: Missing website or article data</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-8">
      <div className="mb-8 text-center">
        <div className="flex items-center justify-center mb-6">
          <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">📝</span>
          </div>
          <span className="ml-3 text-2xl font-bold text-gray-800">BLOGBUSTER</span>
        </div>
        <h2 className="text-2xl font-bold mb-4">Generated Content Preview</h2>
      </div>

      <div className="space-y-6">
        <Card className="p-6">
          <h3 className="text-xl font-semibold mb-4">Website Analysis</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">Title</h4>
              <p className="text-gray-600">{websiteInfo.title}</p>
            </div>
            <div>
              <h4 className="font-medium">Description</h4>
              <p className="text-gray-600">{websiteInfo.description}</p>
            </div>
            <div>
              <h4 className="font-medium">Main Topics</h4>
              <div className="flex flex-wrap gap-2">
                {websiteInfo.mainTopics.map((topic, index) => (
                  <span key={index} className="bg-orange-100 text-orange-800 px-2 py-1 rounded text-sm">
                    {topic}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-xl font-semibold mb-4">Generated Article</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">Title</h4>
              <p className="text-gray-600">{article.title}</p>
            </div>
            <div>
              <h4 className="font-medium">Meta Description</h4>
              <p className="text-gray-600">{article.metaDescription}</p>
            </div>
            <div>
              <h4 className="font-medium">Keywords</h4>
              <div className="flex flex-wrap gap-2">
                {article.keywords.map((keyword, index) => (
                  <span key={index} className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium">Excerpt</h4>
              <p className="text-gray-600">{article.excerpt}</p>
            </div>
            <div>
              <h4 className="font-medium">Content Preview</h4>
              <div
                className="prose prose-sm max-w-none mt-2"
                dangerouslySetInnerHTML={{ __html: article.content.substring(0, 500) + '...' }}
              />
            </div>
          </div>
        </Card>

        <div className="flex justify-end">
          <Button
            onClick={handleContinue}
            className="bg-orange-500 hover:bg-orange-600"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              'Continue'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};
