
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface TopicGenerationProps {
  onNext: () => void;
}

export const TopicGeneration = ({ onNext }: TopicGenerationProps) => {
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);

  const suggestedTopics = [
    {
      title: "Top Strategies to Win AI Quiz Games and Maximize Rewards",
      category: "AI Quiz Games",
      keywords: "AI quiz games, winning quiz strategies, quiz game tips, improve quiz..."
    },
    {
      title: "How Blockchain Rewards Are Revolutionizing Online Gaming",
      category: "Blockchain Rewards", 
      keywords: "blockchain rewards, gaming with blockchain, digital token rewards, blockchain..."
    },
    {
      title: "Interactive Learning Through AI-Powered Quiz Contests",
      category: "Interactive Learning",
      keywords: "interactive learning, AI-powered quizzes, gamified education, learning through..."
    }
  ];

  const handleTopicSelect = (title: string) => {
    setSelectedTopics(prev => 
      prev.includes(title) 
        ? prev.filter(t => t !== title)
        : [...prev, title]
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-8">
      <div className="mb-8 text-center">
        <div className="flex items-center justify-center mb-6">
          <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">📝</span>
          </div>
          <span className="ml-3 text-2xl font-bold text-gray-800">BLOGBUSTER</span>
        </div>
        
        <Button className="w-full max-w-md bg-orange-500 hover:bg-orange-600 text-white py-3 mb-8">
          All set! Let's start your first article! →
        </Button>
      </div>

      <Card className="p-8 bg-white">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold">Generate new article</h2>
          <button className="text-gray-400 hover:text-gray-600">×</button>
        </div>

        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">💡</span>
          </div>
          <p className="text-lg text-gray-600">Start by generating your first SEO article</p>
        </div>

        <div className="space-y-4 mb-8">
          {suggestedTopics.map((topic, index) => (
            <div
              key={index}
              className={`border rounded-lg p-4 cursor-pointer transition-all ${
                selectedTopics.includes(topic.title)
                  ? 'border-orange-500 bg-orange-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleTopicSelect(topic.title)}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium text-gray-900">{topic.title}</h3>
                <div className="flex space-x-2">
                  <Badge variant="secondary" className="bg-orange-100 text-orange-700">
                    {topic.category}
                  </Badge>
                  <Button
                    size="sm"
                    className="bg-orange-500 hover:bg-orange-600 text-white"
                  >
                    Generate now ⚡
                  </Button>
                </div>
              </div>
              <p className="text-sm text-gray-500">Keywords: {topic.keywords}</p>
            </div>
          ))}
        </div>

        <div className="text-center">
          <h3 className="text-xl font-semibold mb-2">... Or choose your own topics</h3>
          <p className="text-gray-600 mb-6">Just enter article ideas and info and we will generate a high quality article for you!</p>
          
          <div className="flex justify-center space-x-2 mb-6">
            <div className="w-3 h-3 bg-orange-200 rounded-full"></div>
            <div className="w-3 h-3 bg-orange-300 rounded-full"></div>
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <div className="w-3 h-3 bg-orange-200 rounded-full"></div>
          </div>

          <Button
            onClick={onNext}
            className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3"
          >
            Continue to Dashboard
          </Button>
        </div>
      </Card>
    </div>
  );
};
