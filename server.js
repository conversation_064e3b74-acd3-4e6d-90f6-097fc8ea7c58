import express from 'express';
import cors from 'cors';
import axios from 'axios';

const app = express();
const port = 3000;

// Configure CORS with specific options
const corsOptions = {
    origin: 'http://localhost:8081',
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true,
    optionsSuccessStatus: 200
};

// Apply CORS configuration
app.use(cors(corsOptions));

// Parse JSON bodies
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
});

app.get('/api/analyze-website', async (req, res) => {
    try {
        const { url } = req.query;

        if (!url) {
            return res.status(400).json({ error: 'URL is required' });
        }

        console.log('Fetching URL:', url);

        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            },
            timeout: 30000,
            maxRedirects: 5,
            validateStatus: function (status) {
                return status >= 200 && status < 300;
            }
        });

        console.log('Response received, status:', response.status);
        res.json({ data: response.data });
    } catch (error) {
        console.error('Error details:', {
            message: error.message,
            code: error.code,
            response: error.response?.status,
            data: error.response?.data
        });

        if (error.code === 'ECONNREFUSED') {
            res.status(503).json({
                error: 'Service temporarily unavailable',
                details: 'Could not connect to target website'
            });
        } else if (error.code === 'ETIMEDOUT') {
            res.status(504).json({
                error: 'Gateway timeout',
                details: 'Request to target website timed out'
            });
        } else {
            res.status(500).json({
                error: 'Failed to fetch website content',
                details: error.message
            });
        }
    }
});

// Handle preflight requests
app.options('*', cors(corsOptions));

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        details: err.message
    });
});

app.listen(port, () => {
    console.log(`Proxy server running at http://localhost:${port}`);
}); 