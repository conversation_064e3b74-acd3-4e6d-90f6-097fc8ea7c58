import axios from 'axios';
import * as cheerio from 'cheerio';

export interface WebsiteInfo {
    title: string;
    description: string;
    keywords: string[];
    mainTopics: string[];
    headings: string[];
    links: string[];
}

export const extractWebsiteInfo = async (url: string): Promise<WebsiteInfo> => {
    try {
        // Ensure URL has protocol
        if (!url.startsWith('http')) {
            url = `https://${url}`;
        }

        const response = await axios.get(url);
        const html = response.data;
        const $ = cheerio.load(html);

        // Extract meta information
        const title = $('title').text() || $('meta[property="og:title"]').attr('content') || '';
        const description = $('meta[name="description"]').attr('content') ||
            $('meta[property="og:description"]').attr('content') || '';
        const keywords = $('meta[name="keywords"]').attr('content')?.split(',').map(k => k.trim()) || [];

        // Extract headings
        const headings: string[] = [];
        $('h1, h2, h3').each((_, element) => {
            const headingText = $(element).text().trim();
            if (headingText) {
                headings.push(headingText);
            }
        });

        // Extract main topics based on content analysis
        const mainTopics = new Set<string>();
        $('p').each((_, element) => {
            const text = $(element).text().trim();
            if (text.length > 50) { // Only consider substantial paragraphs
                const words = text.split(' ')
                    .filter(word => word.length > 4) // Filter out small words
                    .map(word => word.toLowerCase());
                words.forEach(word => mainTopics.add(word));
            }
        });

        // Extract links
        const links: string[] = [];
        $('a').each((_, element) => {
            const href = $(element).attr('href');
            if (href && href.startsWith('http')) {
                links.push(href);
            }
        });

        return {
            title,
            description,
            keywords,
            mainTopics: Array.from(mainTopics).slice(0, 10), // Get top 10 topics
            headings,
            links: links.slice(0, 20) // Get top 20 links
        };
    } catch (error) {
        console.error('Error extracting website info:', error);
        throw new Error('Failed to extract website information');
    }
}; 