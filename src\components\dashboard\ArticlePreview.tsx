import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface Article {
    id: number;
    title: string;
    description: string;
    content: string;
    tag: string;
    status: string;
    author: string;
    date: string;
    image: string;
}

interface ArticlePreviewProps {
    article: Article | null;
    onClose: () => void;
}

export const ArticlePreview = ({ article, onClose }: ArticlePreviewProps) => {
    if (!article) return null;

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-4xl h-[90vh] p-6 bg-white overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-semibold">Article Preview</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>

                <div className="space-y-6">
                    {/* Article Header */}
                    <div className="border-b pb-4">
                        <h1 className="text-3xl font-bold mb-3">{article.title}</h1>
                        <div className="flex items-center text-sm text-gray-600 space-x-4">
                            <span>By {article.author}</span>
                            <span>•</span>
                            <span>{article.date}</span>
                            <span>•</span>
                            <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded">
                                {article.tag}
                            </span>
                        </div>
                    </div>

                    {/* Article Description */}
                    <div className="text-lg text-gray-600 italic border-l-4 border-orange-500 pl-4">
                        {article.description}
                    </div>

                    {/* Article Content */}
                    <div
                        className="prose max-w-none"
                        dangerouslySetInnerHTML={{ __html: article.content }}
                    />
                </div>

                <div className="mt-8 flex justify-end">
                    <Button
                        onClick={onClose}
                        variant="outline"
                    >
                        Close Preview
                    </Button>
                </div>
            </Card>
        </div>
    );
}; 