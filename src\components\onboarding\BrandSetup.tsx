import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';

interface BrandSetupProps {
  onNext: () => void;
  onData: (data: any) => void;
  initialData: {
    name: string;
    description: string;
    keywords: string[];
  };
}

export const BrandSetup = ({ onNext, onData, initialData }: BrandSetupProps) => {
  const [brandInfo, setBrandInfo] = useState({
    name: initialData.name,
    description: initialData.description,
    targetAudience: '',
    tone: 'professional',
    keywords: initialData.keywords
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      onData(brandInfo);
      onNext();
    }, 2000);
  };

  if (isLoading) {
    return (
      <div className="w-full max-w-4xl mx-auto p-8 text-center">
        <div className="mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">📝</span>
            </div>
            <span className="ml-3 text-2xl font-bold text-gray-800">BLOGBUSTER</span>
          </div>
        </div>

        <div className="animate-spin w-12 h-12 border-4 border-orange-200 border-t-orange-500 rounded-full mx-auto mb-6"></div>
        <p className="text-lg text-orange-600 mb-4">Loading your info... This can take up to 1-2 minutes</p>

        <Card className="max-w-3xl mx-auto p-6 bg-white/80 backdrop-blur">
          <div className="text-center mb-6">
            <h2 className="text-xl font-semibold mb-4">Confirm brand info</h2>
            <p className="text-gray-600">We are analyzing your brand info, audience and tone. You will be able to fine-tune them if needed!</p>
          </div>

          <div className="flex justify-center space-x-2">
            <div className="w-3 h-3 bg-orange-300 rounded-full"></div>
            <div className="w-3 h-3 bg-orange-400 rounded-full"></div>
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <div className="w-3 h-3 bg-orange-300 rounded-full"></div>
            <div className="w-3 h-3 bg-orange-200 rounded-full"></div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-8">
      <div className="mb-8 text-center">
        <div className="flex items-center justify-center mb-6">
          <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">📝</span>
          </div>
          <span className="ml-3 text-2xl font-bold text-gray-800">BLOGBUSTER</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <Card className="p-6 h-full">
            <h2 className="text-xl font-semibold mb-4">Brand Info</h2>
            <p className="text-gray-600 mb-6">Information used to generate bespoke and relevant articles to your business</p>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="brandName">Brand Name</Label>
                <Input
                  id="brandName"
                  placeholder="EasyChef"
                  value={brandInfo.name}
                  onChange={(e) => setBrandInfo({ ...brandInfo, name: e.target.value })}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="EasyChef is a cutting-edge app that leverages AI to create personalized recipes tailored to your available ingredients, dietary preferences, and cooking style..."
                  value={brandInfo.description}
                  onChange={(e) => setBrandInfo({ ...brandInfo, description: e.target.value })}
                  className="mt-1 h-32"
                />
              </div>

              <div>
                <Label htmlFor="audience">Target Audience</Label>
                <Textarea
                  id="audience"
                  placeholder="The target audience includes busy individuals, health-conscious eaters, food enthusiasts..."
                  value={brandInfo.targetAudience}
                  onChange={(e) => setBrandInfo({ ...brandInfo, targetAudience: e.target.value })}
                  className="mt-1 h-24"
                />
              </div>

              <div>
                <Label htmlFor="keywords">Keywords</Label>
                <Input
                  id="keywords"
                  placeholder="Enter keywords separated by commas"
                  value={brandInfo.keywords.join(', ')}
                  onChange={(e) => setBrandInfo({ ...brandInfo, keywords: e.target.value.split(',').map(k => k.trim()) })}
                  className="mt-1"
                />
              </div>

              <Button type="submit" className="w-full bg-orange-500 hover:bg-orange-600">
                Save
              </Button>
            </form>
          </Card>
        </div>

        <div>
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Brand Logo</h2>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-4"></div>
              <Button variant="outline">Upload Logo</Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};
