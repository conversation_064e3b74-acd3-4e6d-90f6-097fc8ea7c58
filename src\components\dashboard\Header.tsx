
import { Button } from '@/components/ui/button';

export const Header = () => {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        </div>
        
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            className="border-orange-500 text-orange-600 hover:bg-orange-50"
          >
            💡 Get more topic
          </Button>
          
          <Button className="bg-orange-500 hover:bg-orange-600 text-white">
            Write Article ✏️
          </Button>
          
          <button className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
            <span className="text-sm">👤</span>
          </button>
        </div>
      </div>
    </header>
  );
};
