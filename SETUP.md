# BlogBuster Setup Instructions

## 🚀 Quick Start

Your BlogBuster application is now ready! Follow these steps to get it fully working:

### 1. ✅ Servers Running
- **Frontend**: http://localhost:8081 ✅
- **Backend**: http://localhost:3000 ✅

### 2. 🔑 Configure OpenAI API Key

To enable article generation with ChatGPT, you need to add your OpenAI API key:

1. **Get your API key**:
   - Go to https://platform.openai.com/api-keys
   - Create a new API key
   - Copy the key (starts with `sk-...`)

2. **Add to .env file**:
   - Open the `.env` file in the root directory
   - Replace `your_openai_api_key_here` with your actual API key:
   ```
   OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

3. **Restart the backend server**:
   - Stop the backend server (Ctrl+C in the terminal)
   - Run: `node simple-server.js`

### 3. 🧪 Test the Application

1. **Open the app**: Go to http://localhost:8081
2. **Enter a domain**: Try `https://example.com` first (simple test)
3. **Wait for processing**: The app will:
   - Fetch the website content
   - Analyze the website structure
   - Generate an SEO-optimized article using ChatGPT
4. **View results**: You'll see the website analysis and generated article

### 4. 🎯 Features

- **Website Analysis**: Extracts title, description, keywords, headings, and main topics
- **Smart Content Processing**: Analyzes website content intelligently
- **ChatGPT Integration**: Generates high-quality, SEO-optimized articles
- **Professional UI**: Clean, modern interface with loading states
- **Error Handling**: Comprehensive error handling and user feedback

### 5. 🔧 Troubleshooting

**If article generation fails:**
- Check that your OpenAI API key is correct
- Ensure you have credits in your OpenAI account
- Check the backend logs for error messages

**If website analysis fails:**
- Some websites block automated requests
- Try with different websites
- Check the backend logs for specific errors

### 6. 💡 Usage Tips

- **Best websites to test**: News sites, blogs, company websites
- **Avoid**: Sites with heavy JavaScript or anti-bot protection
- **Article quality**: The generated articles are based on the website content analysis
- **Customization**: You can modify the tone and length in the code

## 🎉 You're Ready!

Your BlogBuster application is now fully functional and ready to generate SEO-optimized articles from any website!
