import express from 'express';
import cors from 'cors';
import axios from 'axios';
import { load } from 'cheerio';
import OpenAI from 'openai';
import { config } from 'dotenv';

config();

const app = express();
const port = 3000;

// Initialize OpenAI
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

// Website analysis function
async function extractWebsiteInfo(html, url) {
    try {
        const $ = load(html);

        // Extract meta information
        const title = $('title').text() || $('meta[property="og:title"]').attr('content') || '';
        const description = $('meta[name="description"]').attr('content') ||
            $('meta[property="og:description"]').attr('content') || '';
        const keywords = $('meta[name="keywords"]').attr('content')?.split(',').map(k => k.trim()) || [];

        // Extract headings
        const headings = [];
        $('h1, h2, h3').each((_, element) => {
            const headingText = $(element).text().trim();
            if (headingText && headingText.length > 0) {
                headings.push(headingText);
            }
        });

        // Extract main content paragraphs
        const contentParagraphs = [];
        $('p').each((_, element) => {
            const text = $(element).text().trim();
            if (text.length > 50) { // Only consider substantial paragraphs
                contentParagraphs.push(text);
            }
        });

        // Extract main topics based on content analysis
        const mainTopics = new Set();
        contentParagraphs.forEach(paragraph => {
            const words = paragraph.split(' ')
                .filter(word => word.length > 4) // Filter out small words
                .map(word => word.toLowerCase().replace(/[^a-z]/g, ''))
                .filter(word => word.length > 0);
            words.forEach(word => mainTopics.add(word));
        });

        // Extract links
        const links = [];
        $('a').each((_, element) => {
            const href = $(element).attr('href');
            if (href && href.startsWith('http')) {
                links.push(href);
            }
        });

        return {
            title: title.trim(),
            description: description.trim(),
            keywords,
            mainTopics: Array.from(mainTopics).slice(0, 15), // Get top 15 topics
            headings: headings.slice(0, 10), // Get top 10 headings
            links: links.slice(0, 20), // Get top 20 links
            contentParagraphs: contentParagraphs.slice(0, 5), // Get first 5 paragraphs
            url
        };
    } catch (error) {
        console.error('Error extracting website info:', error);
        throw new Error('Failed to extract website information');
    }
}

// Article generation function
async function generateArticle(websiteInfo, topic, tone = 'professional', targetLength = 1000) {
    try {
        const prompt = `
Write a high-quality SEO-optimized article based on the following website information:

Website Title: ${websiteInfo.title}
Website Description: ${websiteInfo.description}
Website URL: ${websiteInfo.url}
Main Topics: ${websiteInfo.mainTopics.join(', ')}
Keywords: ${websiteInfo.keywords.join(', ')}
Headings: ${websiteInfo.headings.join(', ')}
Content Sample: ${websiteInfo.contentParagraphs.join(' ').substring(0, 500)}...

Requirements:
- Topic: ${topic || 'Choose a relevant topic based on the website content'}
- Tone: ${tone}
- Length: Approximately ${targetLength} words
- Include relevant headings (H2, H3)
- Make it SEO-friendly with proper keyword usage
- Write in a natural, engaging style
- Include a compelling meta description
- Focus on providing value to readers

Format the response as JSON with the following structure:
{
  "title": "Article Title",
  "content": "Full article content with HTML tags for headings and paragraphs",
  "metaDescription": "SEO meta description (150-160 characters)",
  "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
  "excerpt": "Brief article excerpt (2-3 sentences)"
}

Make sure the JSON is valid and properly formatted.`;

        console.log('Generating article with ChatGPT...');
        const completion = await openai.chat.completions.create({
            model: "gpt-4",
            messages: [
                {
                    role: "system",
                    content: "You are an expert content writer and SEO specialist. Always respond with valid JSON format."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 3000
        });

        const response = completion.choices[0]?.message?.content;
        if (!response) {
            throw new Error('No response from ChatGPT');
        }

        console.log('ChatGPT response received');
        return JSON.parse(response);
    } catch (error) {
        console.error('Error generating article:', error);
        throw new Error('Failed to generate article: ' + error.message);
    }
}

// Configure CORS to allow requests from frontend
app.use(cors({
    origin: ['http://localhost:8081', 'http://localhost:8082'],
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true
}));

// Parse JSON bodies
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
});

// Test endpoint
app.get('/api/test', (req, res) => {
    console.log('Test endpoint called');
    res.json({ message: 'API is working!', timestamp: new Date().toISOString() });
});

// Complete website analysis and article generation endpoint
app.post('/api/process-domain', async (req, res) => {
    try {
        const { url, topic, tone, targetLength } = req.body;

        if (!url) {
            console.log('Error: No URL provided');
            return res.status(400).json({ error: 'URL is required' });
        }

        console.log('Processing domain:', url);
        console.log('Options:', { topic, tone, targetLength });

        // Step 1: Fetch website content
        const isAmazon = url.includes('amazon');
        const headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        };

        if (isAmazon) {
            headers['sec-ch-ua'] = '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"';
            headers['sec-ch-ua-mobile'] = '?0';
            headers['sec-ch-ua-platform'] = '"Windows"';
            headers['Sec-Fetch-Dest'] = 'document';
            headers['Sec-Fetch-Mode'] = 'navigate';
            headers['Sec-Fetch-Site'] = 'none';
            headers['Sec-Fetch-User'] = '?1';
        }

        console.log('Fetching website content...');
        const response = await axios.get(url, {
            headers,
            timeout: 30000,
            maxRedirects: 5,
            validateStatus: function (status) {
                return status >= 200 && status < 400;
            }
        });

        console.log('Website content fetched, status:', response.status);

        // Step 2: Extract website information
        console.log('Analyzing website content...');
        const websiteInfo = await extractWebsiteInfo(response.data, url);
        console.log('Website analysis complete:', {
            title: websiteInfo.title,
            topicsCount: websiteInfo.mainTopics.length,
            headingsCount: websiteInfo.headings.length
        });

        // Step 3: Generate article using ChatGPT
        if (!process.env.OPENAI_API_KEY) {
            console.log('Warning: No OpenAI API key found, returning analysis only');
            return res.json({
                websiteInfo,
                article: {
                    title: 'Article Generation Unavailable',
                    content: 'Please configure OpenAI API key to generate articles.',
                    metaDescription: 'Article generation requires OpenAI API key',
                    keywords: websiteInfo.keywords,
                    excerpt: 'Configure OpenAI API key to enable article generation.'
                }
            });
        }

        console.log('Generating article...');
        const article = await generateArticle(websiteInfo, topic, tone, targetLength);
        console.log('Article generated successfully');

        res.json({ websiteInfo, article });
    } catch (error) {
        console.error('Error details:', {
            message: error.message,
            code: error.code,
            response: error.response?.status,
            data: error.response?.data
        });

        if (error.code === 'ECONNREFUSED') {
            res.status(503).json({
                error: 'Service temporarily unavailable',
                details: 'Could not connect to target website'
            });
        } else if (error.code === 'ETIMEDOUT') {
            res.status(504).json({
                error: 'Gateway timeout',
                details: 'Request to target website timed out'
            });
        } else {
            res.status(500).json({
                error: 'Failed to fetch website content',
                details: error.message
            });
        }
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        details: err.message
    });
});

app.listen(port, () => {
    console.log(`Proxy server running at http://localhost:${port}`);
});

// Keep the process alive
process.stdin.resume();
