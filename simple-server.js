import express from 'express';
import cors from 'cors';
import axios from 'axios';

const app = express();
const port = 3000;

// Configure CORS to allow requests from frontend
app.use(cors({
    origin: ['http://localhost:8081', 'http://localhost:8082'],
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true
}));

// Parse JSON bodies
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
});

// Test endpoint
app.get('/api/test', (req, res) => {
    console.log('Test endpoint called');
    res.json({ message: 'API is working!', timestamp: new Date().toISOString() });
});

// Website analysis endpoint
app.get('/api/analyze-website', async (req, res) => {
    try {
        const { url } = req.query;

        if (!url) {
            console.log('Error: No URL provided');
            return res.status(400).json({ error: 'URL is required' });
        }

        console.log('Received request for URL:', url);
        console.log('Request headers:', req.headers);
        console.log('Request origin:', req.get('origin'));

        // Special handling for different websites
        const isAmazon = url.includes('amazon');
        const headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        };

        if (isAmazon) {
            headers['sec-ch-ua'] = '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"';
            headers['sec-ch-ua-mobile'] = '?0';
            headers['sec-ch-ua-platform'] = '"Windows"';
            headers['Sec-Fetch-Dest'] = 'document';
            headers['Sec-Fetch-Mode'] = 'navigate';
            headers['Sec-Fetch-Site'] = 'none';
            headers['Sec-Fetch-User'] = '?1';
        }

        console.log('Making request with headers:', headers);

        const response = await axios.get(url, {
            headers,
            timeout: 30000,
            maxRedirects: 5,
            validateStatus: function (status) {
                return status >= 200 && status < 400;
            }
        });

        console.log('Response received, status:', response.status);
        res.json({ data: response.data });
    } catch (error) {
        console.error('Error details:', {
            message: error.message,
            code: error.code,
            response: error.response?.status,
            data: error.response?.data
        });

        if (error.code === 'ECONNREFUSED') {
            res.status(503).json({
                error: 'Service temporarily unavailable',
                details: 'Could not connect to target website'
            });
        } else if (error.code === 'ETIMEDOUT') {
            res.status(504).json({
                error: 'Gateway timeout',
                details: 'Request to target website timed out'
            });
        } else {
            res.status(500).json({
                error: 'Failed to fetch website content',
                details: error.message
            });
        }
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        details: err.message
    });
});

app.listen(port, () => {
    console.log(`Proxy server running at http://localhost:${port}`);
});

// Keep the process alive
process.stdin.resume();
