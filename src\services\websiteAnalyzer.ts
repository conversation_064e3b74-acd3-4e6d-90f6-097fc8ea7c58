import axios from 'axios';
import * as cheerio from 'cheerio';

export interface WebsiteInfo {
    title: string;
    description: string;
    keywords: string[];
    mainTopics: string[];
    headings: string[];
    metaTags: {
        [key: string]: string;
    };
}

export class WebsiteAnalyzer {
    private readonly MAX_RETRIES = 3;
    private readonly RETRY_DELAY = 1000; // 1 second
    private readonly API_BASE_URL = 'http://localhost:3000';

    private async delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    private async fetchWithRetry(url: string, retryCount = 0): Promise<string> {
        try {
            console.log(`Attempt ${retryCount + 1} - Fetching website:`, url);
            const encodedUrl = encodeURIComponent(url);
            const response = await axios.get(`${this.API_BASE_URL}/api/analyze-website?url=${encodedUrl}`, {
                withCredentials: true,
                timeout: 30000,
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });

            if (!response.data || !response.data.data) {
                throw new Error('Invalid response format from proxy server');
            }

            return response.data.data;
        } catch (error: any) {
            console.error(`Attempt ${retryCount + 1} failed:`, {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });

            // Check if we should retry
            if (retryCount < this.MAX_RETRIES &&
                (!error.response || error.response.status >= 500 || error.code === 'ECONNABORTED')) {
                const delay = this.RETRY_DELAY * Math.pow(2, retryCount); // Exponential backoff
                console.log(`Retrying in ${delay}ms...`);
                await this.delay(delay);
                return this.fetchWithRetry(url, retryCount + 1);
            }

            // Handle specific error cases
            if (error.response?.status === 404) {
                throw new Error('Website not found');
            }
            if (error.response?.status === 503) {
                throw new Error('Service temporarily unavailable. Please try again later.');
            }
            if (error.response?.status === 504) {
                throw new Error('Request timed out. Please try again.');
            }
            if (error.code === 'ECONNREFUSED') {
                throw new Error('Could not connect to the server. Please ensure the server is running.');
            }
            if (error.code === 'ECONNABORTED') {
                throw new Error('Request timed out. Please try again.');
            }

            throw new Error(`Failed to fetch website content: ${error.response?.data?.details || error.message}`);
        }
    }

    private extractMetaTags($: cheerio.CheerioAPI): { [key: string]: string } {
        const metaTags: { [key: string]: string } = {};
        $('meta').each((_, element) => {
            const name = $(element).attr('name') || $(element).attr('property');
            const content = $(element).attr('content');
            if (name && content) {
                metaTags[name] = content;
            }
        });
        return metaTags;
    }

    private extractMainTopics($: cheerio.CheerioAPI): string[] {
        const topics = new Set<string>();

        // Extract topics from headings
        $('h1, h2, h3').each((_, element) => {
            const text = $(element).text().trim();
            if (text) topics.add(text);
        });

        // Extract topics from navigation
        $('nav a').each((_, element) => {
            const text = $(element).text().trim();
            if (text) topics.add(text);
        });

        return Array.from(topics);
    }

    private extractHeadings($: cheerio.CheerioAPI): string[] {
        const headings: string[] = [];
        $('h1, h2, h3, h4, h5, h6').each((_, element) => {
            const text = $(element).text().trim();
            if (text) headings.push(text);
        });
        return headings;
    }

    private extractKeywords($: cheerio.CheerioAPI): string[] {
        // Try to get keywords from meta tags
        const keywordsMetaTag = $('meta[name="keywords"]').attr('content');
        if (keywordsMetaTag) {
            return keywordsMetaTag.split(',').map(k => k.trim());
        }

        // If no keywords meta tag, extract common words from content
        const content = $('p').text();
        const words = content.toLowerCase()
            .split(/\W+/)
            .filter(word => word.length > 3)
            .filter(word => !['this', 'that', 'have', 'from', 'they', 'will'].includes(word));

        const wordFrequency: { [key: string]: number } = {};
        words.forEach(word => {
            wordFrequency[word] = (wordFrequency[word] || 0) + 1;
        });

        return Object.entries(wordFrequency)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([word]) => word);
    }

    public async analyze(url: string): Promise<WebsiteInfo> {
        try {
            console.log('Starting website analysis for:', url);

            // Validate URL format
            try {
                new URL(url);
            } catch {
                throw new Error('Invalid URL format. Please enter a valid URL.');
            }

            const html = await this.fetchWithRetry(url);
            if (!html) {
                throw new Error('No content received from the website');
            }

            const $ = cheerio.load(html);

            const websiteInfo: WebsiteInfo = {
                title: $('title').text().trim() || '',
                description: $('meta[name="description"]').attr('content')?.trim() || '',
                keywords: this.extractKeywords($),
                mainTopics: this.extractMainTopics($),
                headings: this.extractHeadings($),
                metaTags: this.extractMetaTags($)
            };

            console.log('Analysis completed successfully');
            return websiteInfo;
        } catch (error: any) {
            console.error('Error analyzing website:', error);
            throw new Error(error.message || 'Failed to analyze website');
        }
    }
} 