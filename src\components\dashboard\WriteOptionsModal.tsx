import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface WriteOptionsModalProps {
    onClose: () => void;
    onGenerateWithAI: () => void;
    onWriteManually: () => void;
}

export const WriteOptionsModal = ({ onClose, onGenerateWithAI, onWriteManually }: WriteOptionsModalProps) => {
    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md p-6 bg-white">
                <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                            <span className="text-white font-bold">📝</span>
                        </div>
                        <h2 className="text-xl font-semibold">BlogBuster</h2>
                    </div>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>

                <div className="text-center mb-8">
                    <p className="text-gray-600 mb-2">
                        Add a few words, a full brief or documents link
                    </p>
                    <p className="text-gray-600">
                        to auto-generate a unique article.
                    </p>
                </div>

                <div className="space-y-4">
                    <Button
                        onClick={onGenerateWithAI}
                        className="w-full bg-orange-500 hover:bg-orange-600 text-white flex items-center justify-center gap-2"
                    >
                        <span>Generate with AI</span>
                        <span>⚡</span>
                    </Button>

                    <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                            <div className="w-full border-t border-gray-300" />
                        </div>
                        <div className="relative flex justify-center text-sm">
                            <span className="px-2 bg-white text-gray-500">Or</span>
                        </div>
                    </div>

                    <Button
                        onClick={onWriteManually}
                        variant="outline"
                        className="w-full"
                    >
                        write manually
                    </Button>
                </div>
            </Card>
        </div>
    );
}; 