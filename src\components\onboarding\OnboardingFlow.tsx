import { useState } from 'react';
import { DomainEntry } from './DomainEntry';
import { BrandSetup } from './BrandSetup';
import { TopicGeneration } from './TopicGeneration';
import { OnboardingComplete } from './OnboardingComplete';
import { WebsiteInfo } from '@/services/websiteAnalyzer';
import { GeneratedArticle } from '@/services/articleGenerator';

interface OnboardingFlowProps {
  onComplete: () => void;
}

interface OnboardingData {
  domain?: string;
  websiteInfo?: WebsiteInfo;
  article?: GeneratedArticle;
  brandData?: any;
}

export const OnboardingFlow = ({ onComplete }: OnboardingFlowProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({});

  const handleNextStep = () => {
    setCurrentStep(prev => prev + 1);
  };

  const handleComplete = () => {
    // Here you can save all the collected data before completing
    console.log('Onboarding data:', onboardingData);
    onComplete();
  };

  const handleDomainData = (data: { domain: string; websiteInfo: WebsiteInfo; article: GeneratedArticle }) => {
    setOnboardingData(prev => ({
      ...prev,
      ...data
    }));
  };

  const handleBrandData = (data: any) => {
    setOnboardingData(prev => ({
      ...prev,
      brandData: data
    }));
  };

  const steps = [
    <DomainEntry key="domain" onNext={handleNextStep} onData={handleDomainData} />,
    <BrandSetup key="brand" onNext={handleNextStep} onData={handleBrandData} />,
    <TopicGeneration key="topics" onNext={handleNextStep} />,
    <OnboardingComplete key="complete" onComplete={handleComplete} />
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 to-amber-50">
      {steps[currentStep]}
    </div>
  );
};
