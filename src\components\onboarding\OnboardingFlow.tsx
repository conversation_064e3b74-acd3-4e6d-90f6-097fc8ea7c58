import { useState } from 'react';
import { DomainEntry } from './DomainEntry';
import { BrandSetup } from './BrandSetup';
import { TopicGeneration } from './TopicGeneration';
import { OnboardingComplete } from './OnboardingComplete';
import { WebsiteInfo } from '@/lib/services/websiteService';
import { GeneratedArticle } from '@/lib/services/articleService';

interface OnboardingFlowProps {
  onComplete: () => void;
}

export const OnboardingFlow = ({ onComplete }: OnboardingFlowProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [websiteInfo, setWebsiteInfo] = useState<WebsiteInfo | null>(null);
  const [article, setArticle] = useState<GeneratedArticle | null>(null);
  const [brandData, setBrandData] = useState({});

  const handleNextStep = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const handleDomainData = (data: { websiteInfo: WebsiteInfo; article: GeneratedArticle }) => {
    setWebsiteInfo(data.websiteInfo);
    setArticle(data.article);
  };

  const handleComplete = () => {
    // Here you can save all the collected data or perform any final actions
    onComplete();
  };

  const steps = [
    <DomainEntry key="domain" onNext={handleNextStep} onData={handleDomainData} />,
    <BrandSetup
      key="brand"
      onNext={handleNextStep}
      onData={setBrandData}
      initialData={{
        name: websiteInfo?.title || '',
        description: websiteInfo?.description || '',
        keywords: websiteInfo?.keywords || []
      }}
    />,
    <TopicGeneration
      key="topics"
      onNext={handleNextStep}
      websiteInfo={websiteInfo}
      article={article}
    />,
    <OnboardingComplete key="complete" onComplete={handleComplete} />
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 to-amber-50">
      {steps[currentStep]}
    </div>
  );
};
