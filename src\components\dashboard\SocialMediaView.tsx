
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';

export const SocialMediaView = () => {
  const [tweetText, setTweetText] = useState('');
  const [imagePrompt, setImagePrompt] = useState('');

  const scheduledPosts = [
    {
      id: 1,
      platform: 'Twitter',
      content: 'Master AI Quiz Games: Discover winning strategies and maximize your rewards! 🧠💰 #AIQuiz #Gaming',
      imagePrompt: 'AI brain with quiz bubbles and golden rewards',
      scheduledFor: '2025-06-01 10:00 AM',
      status: 'scheduled'
    },
    {
      id: 2,
      platform: 'LinkedIn',
      content: 'How Blockchain is revolutionizing online gaming rewards - a comprehensive guide for modern gamers',
      imagePrompt: 'Blockchain network with gaming elements',
      scheduledFor: '2025-06-01 2:00 PM',
      status: 'scheduled'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Social Media Management</h1>
        <Button className="bg-orange-500 hover:bg-orange-600 text-white">
          📱 Connect Accounts
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Create Social Post</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Platform
              </label>
              <div className="flex space-x-2">
                <Button variant="outline" className="flex items-center">
                  🐦 Twitter
                </Button>
                <Button variant="outline" className="flex items-center">
                  💼 LinkedIn
                </Button>
                <Button variant="outline" className="flex items-center">
                  📘 Facebook
                </Button>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Post Content
              </label>
              <Textarea
                placeholder="Write your engaging social media post..."
                value={tweetText}
                onChange={(e) => setTweetText(e.target.value)}
                className="h-24"
              />
              <div className="text-right text-sm text-gray-500 mt-1">
                {tweetText.length}/280
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                AI Image Prompt
              </label>
              <Input
                placeholder="Describe the image you want to generate..."
                value={imagePrompt}
                onChange={(e) => setImagePrompt(e.target.value)}
              />
            </div>
            
            <div className="flex space-x-2">
              <Button className="bg-orange-500 hover:bg-orange-600 text-white flex-1">
                🎨 Generate & Post
              </Button>
              <Button variant="outline" className="flex-1">
                📅 Schedule Post
              </Button>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">AI-Generated Images</h2>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="aspect-square bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-2xl">🧠</span>
            </div>
            <div className="aspect-square bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-2xl">🎯</span>
            </div>
            <div className="aspect-square bg-gradient-to-br from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-2xl">📈</span>
            </div>
            <div className="aspect-square bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-2xl">⚡</span>
            </div>
          </div>
          
          <Button className="w-full mt-4 bg-orange-500 hover:bg-orange-600 text-white">
            🎨 Generate New Images
          </Button>
        </Card>
      </div>

      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Scheduled Posts</h2>
        
        <div className="space-y-4">
          {scheduledPosts.map((post) => (
            <div key={post.id} className="flex items-start space-x-4 p-4 border rounded-lg">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                {post.platform === 'Twitter' ? '🐦' : '💼'}
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <Badge className="bg-blue-100 text-blue-700">
                    {post.platform}
                  </Badge>
                  <Badge className="bg-green-100 text-green-700">
                    {post.status}
                  </Badge>
                </div>
                
                <p className="text-gray-900 mb-2">{post.content}</p>
                <p className="text-sm text-gray-500 mb-1">
                  Image: {post.imagePrompt}
                </p>
                <p className="text-sm text-gray-500">
                  Scheduled for: {post.scheduledFor}
                </p>
              </div>
              
              <div className="flex space-x-2">
                <Button size="sm" variant="outline">
                  Edit
                </Button>
                <Button size="sm" variant="outline" className="text-red-600">
                  Cancel
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        {scheduledPosts.length === 0 && (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📅</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No scheduled posts</h3>
            <p className="text-gray-600">Create and schedule your first social media post</p>
          </div>
        )}
      </Card>
    </div>
  );
};
