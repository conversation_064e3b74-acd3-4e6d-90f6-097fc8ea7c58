const express = require('express');
const cors = require('cors');
const axios = require('axios');
const cheerio = require('cheerio');
const OpenAI = require('openai');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 5001;

// Enable CORS for all origins in development
app.use(cors());

// Handle preflight requests
app.options('*', cors());

app.use(express.json());

// Add error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ error: err.message || 'Internal server error' });
});

// Add request logging
app.use((req, res, next) => {
    console.log(`${req.method} ${req.url}`);
    next();
});

// Initialize OpenAI
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

// Extract website information
async function extractWebsiteInfo(url) {
    try {
        // Add user-agent to avoid being blocked
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });
        const html = response.data;
        const $ = cheerio.load(html);

        // Extract meta information
        const title = $('title').text() || $('meta[property="og:title"]').attr('content') || '';
        const description = $('meta[name="description"]').attr('content') ||
            $('meta[property="og:description"]').attr('content') || '';
        const keywords = $('meta[name="keywords"]').attr('content')?.split(',').map(k => k.trim()) || [];

        // Extract headings
        const headings = [];
        $('h1, h2, h3').each((_, element) => {
            const headingText = $(element).text().trim();
            if (headingText) {
                headings.push(headingText);
            }
        });

        // Extract main topics based on content analysis
        const mainTopics = new Set();
        $('p').each((_, element) => {
            const text = $(element).text().trim();
            if (text.length > 50) {
                const words = text.split(' ')
                    .filter(word => word.length > 4)
                    .map(word => word.toLowerCase());
                words.forEach(word => mainTopics.add(word));
            }
        });

        // Extract links
        const links = [];
        $('a').each((_, element) => {
            const href = $(element).attr('href');
            if (href && href.startsWith('http')) {
                links.push(href);
            }
        });

        return {
            title,
            description,
            keywords,
            mainTopics: Array.from(mainTopics).slice(0, 10),
            headings,
            links: links.slice(0, 20)
        };
    } catch (error) {
        console.error('Error extracting website info:', error);
        throw new Error('Failed to extract website information');
    }
}

// Generate article using GPT-4
async function generateArticle(websiteInfo, topic, tone = 'professional', targetLength = 1000) {
    try {
        const prompt = `
      Write a high-quality SEO-optimized article based on the following website information:
      
      Website Title: ${websiteInfo.title}
      Website Description: ${websiteInfo.description}
      Main Topics: ${websiteInfo.mainTopics.join(', ')}
      Keywords: ${websiteInfo.keywords.join(', ')}
      
      Requirements:
      - Topic: ${topic || 'Choose a relevant topic based on the website content'}
      - Tone: ${tone}
      - Length: Approximately ${targetLength} words
      - Include relevant headings (H2, H3)
      - Make it SEO-friendly
      - Write in a natural, engaging style
      - Include a meta description and keywords
      
      Format the response as JSON with the following structure:
      {
        "title": "Article Title",
        "content": "Full article content with HTML tags",
        "metaDescription": "SEO meta description",
        "keywords": ["keyword1", "keyword2", ...],
        "excerpt": "Brief article excerpt"
      }
    `;

        const completion = await openai.chat.completions.create({
            model: "gpt-4",
            messages: [
                {
                    role: "system",
                    content: "You are an expert content writer and SEO specialist."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 2000
        });

        const response = completion.choices[0]?.message?.content;
        if (!response) {
            throw new Error('No response from GPT-4');
        }

        return JSON.parse(response);
    } catch (error) {
        console.error('Error generating article:', error);
        throw new Error('Failed to generate article');
    }
}

// API endpoint to process domain
app.post('/api/process-domain', async (req, res) => {
    try {
        const { url } = req.body;
        if (!url) {
            return res.status(400).json({ error: 'URL is required' });
        }

        // Extract website information
        const websiteInfo = await extractWebsiteInfo(url);

        // Generate article
        const article = await generateArticle(websiteInfo);

        res.json({ websiteInfo, article });
    } catch (error) {
        console.error('Error processing domain:', error);
        res.status(500).json({ error: error.message || 'Failed to process domain' });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
});

app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
}); 