import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowRight, Globe } from 'lucide-react';
import { WebsiteInfo } from '@/lib/services/websiteService';
import { GeneratedArticle } from '@/lib/services/articleService';
import { toast } from '@/components/ui/use-toast';
import axios from 'axios';

interface DomainEntryProps {
  onNext: () => void;
  onData: (data: { websiteInfo: WebsiteInfo; article: GeneratedArticle }) => void;
}

export const DomainEntry = ({ onNext, onData }: DomainEntryProps) => {
  const [domain, setDomain] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!domain.trim()) return;

    setIsLoading(true);
    try {
      // Ensure the URL has a protocol
      const url = domain.startsWith('http') ? domain : `https://${domain}`;

      // Call backend API to process domain
      const response = await axios.post('http://localhost:5001/api/process-domain',
        { url },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 30000 // 30 second timeout
        }
      );

      if (response.data.error) {
        throw new Error(response.data.error);
      }

      const { websiteInfo, article } = response.data;

      // Validate response data
      if (!websiteInfo || !article) {
        throw new Error('Invalid response from server');
      }

      // Pass the data to parent component
      onData({ websiteInfo, article });
      onNext();
    } catch (error) {
      console.error('Error processing domain:', error);
      let errorMessage = 'Failed to process domain. Please try again.';

      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          errorMessage = 'Cannot connect to server. Please make sure the backend server is running.';
        } else if (error.response?.data?.error) {
          errorMessage = error.response.data.error;
        } else if (error.message) {
          errorMessage = error.message;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-8 text-center">
      <div className="mb-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">📝</span>
          </div>
          <span className="ml-3 text-2xl font-bold text-gray-800">BLOGBUSTER</span>
        </div>

        <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4 leading-tight">
          Enter your <span className="bg-orange-200 px-2 py-1 rounded">domain URL</span>,<br />
          and we do the rest
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="mb-8">
        <div className="relative max-w-md mx-auto">
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
            <Globe className="w-5 h-5 text-gray-400" />
          </div>
          <Input
            type="url"
            placeholder="https://yourdomain.com/"
            value={domain}
            onChange={(e) => setDomain(e.target.value)}
            className="pl-12 pr-16 py-4 text-lg border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-0"
            disabled={isLoading}
          />
          <Button
            type="submit"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <ArrowRight className="w-5 h-5" />
            )}
          </Button>
        </div>
      </form>

      {isLoading && (
        <div className="text-gray-600">
          <p className="mb-2">🔍 Analyzing your website...</p>
          <p className="text-sm">This may take a minute or two</p>
        </div>
      )}

      {!isLoading && (
        <div className="text-gray-600">
          <p className="mb-2">🚀 Transform your blog into a SEO traffic magnet, all on autopilot</p>
          <p className="text-sm">Automate, Publish, Grow – All in One</p>
        </div>
      )}
    </div>
  );
};
