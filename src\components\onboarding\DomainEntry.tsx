import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowRight, Globe } from 'lucide-react';
import { WebsiteAnalyzer, WebsiteInfo } from '@/services/websiteAnalyzer';
import { ArticleGenerator, GeneratedArticle } from '@/services/articleGenerator';
import { useToast } from '@/components/ui/use-toast';
import { useState } from 'react';

interface DomainEntryProps {
  onNext: () => void;
  onData: (data: { domain: string; websiteInfo: WebsiteInfo; article: GeneratedArticle }) => void;
}

export const DomainEntry = ({ onNext, onData }: DomainEntryProps) => {
  const [domain, setDomain] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!domain.trim()) return;

    setIsLoading(true);
    try {
      // Initialize services
      const websiteAnalyzer = new WebsiteAnalyzer();
      const articleGenerator = new ArticleGenerator(import.meta.env.VITE_OPENAI_API_KEY || '');

      // Analyze website
      const websiteInfo = await websiteAnalyzer.analyze(domain);

      // Generate article
      const article = await articleGenerator.generateArticle(websiteInfo, {
        tone: 'professional',
        length: 'medium'
      });

      // Pass data to parent
      onData({ domain, websiteInfo, article });
      onNext();
    } catch (error) {
      console.error('Error processing domain:', error);
      toast({
        title: 'Error',
        description: 'Failed to process the domain. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-8 text-center">
      <div className="mb-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">📝</span>
          </div>
          <span className="ml-3 text-2xl font-bold text-gray-800">BLOGBUSTER</span>
        </div>

        <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4 leading-tight">
          Enter your <span className="bg-orange-200 px-2 py-1 rounded">domain URL</span>,<br />
          and we do the rest
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="mb-8">
        <div className="relative max-w-md mx-auto">
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
            <Globe className="w-5 h-5 text-gray-400" />
          </div>
          <Input
            type="url"
            placeholder="https://yourdomain.com/"
            value={domain}
            onChange={(e) => setDomain(e.target.value)}
            className="pl-12 pr-16 py-4 text-lg border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-0"
            disabled={isLoading}
          />
          <Button
            type="submit"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <ArrowRight className="w-5 h-5" />
            )}
          </Button>
        </div>
      </form>

      <div className="text-gray-600">
        <p className="mb-2">🚀 Transform your blog into a SEO traffic magnet, all on autopilot</p>
        <p className="text-sm">Automate, Publish, Grow – All in One</p>
      </div>

      {isLoading && (
        <div className="mt-8">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
          <p className="mt-4 text-sm text-gray-500">Analyzing website and generating content...</p>
        </div>
      )}
    </div>
  );
};
