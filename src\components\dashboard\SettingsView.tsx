
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export const SettingsView = () => {
  const [activeTab, setActiveTab] = useState('brand');

  const tabs = [
    { id: 'brand', label: 'Brand' },
    { id: 'content', label: 'Content' },
    { id: 'account', label: 'Account' },
    { id: 'integrations', label: 'Integrations' },
    { id: 'hosting', label: 'Hosting' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {activeTab === 'brand' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Brand Info</h2>
            <p className="text-gray-600 mb-6">
              Information used to generate bespoke and relevant articles to your business
            </p>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="brandName">Brand Name</Label>
                <Input
                  id="brandName"
                  defaultValue="EasyChef"
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  defaultValue="EasyChef is a cutting-edge app that leverages AI to create personalized recipes tailored to your available ingredients, dietary preferences, and cooking style..."
                  className="mt-1 h-32"
                />
              </div>
              
              <div>
                <Label htmlFor="audience">Target Audience</Label>
                <Textarea
                  id="audience"
                  defaultValue="The target audience includes busy individuals, health-conscious eaters, food enthusiasts..."
                  className="mt-1 h-24"
                />
              </div>
              
              <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                Save
              </Button>
            </div>
          </Card>
          
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Brand Logo</h2>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center mb-4">
              <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-4"></div>
              <Button variant="outline">Upload Logo</Button>
            </div>
            
            <div className="space-y-4">
              <div>
                <Label>Audience location</Label>
                <Select defaultValue="global">
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="global">Global</SelectItem>
                    <SelectItem value="us">United States</SelectItem>
                    <SelectItem value="uk">United Kingdom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>Language by default</Label>
                <Select defaultValue="en-us">
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en-us">English (United States)</SelectItem>
                    <SelectItem value="en-uk">English (United Kingdom)</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'content' && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Article Settings</h2>
          <p className="text-gray-600 mb-6">
            Choose the parameters for all your generated blog articles
          </p>
          
          <div className="space-y-6">
            <div>
              <Label className="text-base font-medium">Article Length Target</Label>
              <div className="mt-3 space-y-2">
                <label className="flex items-center">
                  <input type="radio" name="length" value="short" className="mr-2" />
                  <span>Short (800 - 1,200 words)</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="length" value="medium" className="mr-2" defaultChecked />
                  <span>Medium (1,200 - 1,600 words)</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="length" value="long" className="mr-2" />
                  <span>Long (1,600 - 2,000 words)</span>
                </label>
              </div>
            </div>
            
            <div>
              <Label className="text-base font-medium">Brand mentions</Label>
              <div className="mt-3 space-y-2">
                <label className="flex items-center">
                  <input type="radio" name="mentions" value="none" className="mr-2" defaultChecked />
                  <span>None</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="mentions" value="minimal" className="mr-2" />
                  <span>Minimal</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="mentions" value="regular" className="mr-2" />
                  <span>Regular</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="mentions" value="maximal" className="mr-2" />
                  <span>Maximal</span>
                </label>
              </div>
            </div>
            
            <div>
              <Label className="text-base font-medium">Internal Links</Label>
              <div className="mt-3 space-y-2">
                <label className="flex items-center">
                  <input type="radio" name="links" value="none" className="mr-2" defaultChecked />
                  <span>None</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="links" value="few" className="mr-2" />
                  <span>Few (1-2)</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="links" value="regular" className="mr-2" />
                  <span>Regular (3-4)</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="links" value="many" className="mr-2" />
                  <span>Many (over 5)</span>
                </label>
              </div>
            </div>
            
            <div>
              <Label htmlFor="instructions">Specific Instructions</Label>
              <Textarea
                id="instructions"
                placeholder="Be super friendly"
                className="mt-1"
              />
            </div>
            
            <div>
              <Label>Exclusions</Label>
              <Textarea
                placeholder="Enter topics, words, or phrases to exclude from your articles..."
                className="mt-1"
              />
            </div>
            
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              Save Settings
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};
