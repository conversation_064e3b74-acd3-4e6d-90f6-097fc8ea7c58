import { WebsiteInfo } from './websiteAnalyzer';

export interface ArticleGenerationConfig {
    tone?: 'professional' | 'casual' | 'friendly' | 'formal';
    length?: 'short' | 'medium' | 'long';
    targetAudience?: string;
    keywords?: string[];
}

export interface GeneratedArticle {
    title: string;
    content: string;
    metaDescription: string;
    excerpt: string;
    suggestedTags: string[];
}

export class ArticleGenerator {
    private apiKey: string;
    private apiEndpoint: string;

    constructor(apiKey: string, apiEndpoint: string = 'https://api.openai.com/v1/chat/completions') {
        this.apiKey = apiKey;
        this.apiEndpoint = apiEndpoint;
    }

    private async callGPT4(prompt: string): Promise<string> {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    model: 'gpt-4',
                    messages: [
                        {
                            role: 'system',
                            content: 'You are a professional content writer and SEO expert. Generate high-quality, engaging content that is both informative and optimized for search engines.'
                        },
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    temperature: 0.7
                })
            });

            if (!response.ok) {
                throw new Error('Failed to generate content');
            }

            const data = await response.json();
            return data.choices[0].message.content;
        } catch (error) {
            console.error('Error calling GPT-4:', error);
            throw new Error('Failed to generate content');
        }
    }

    private buildPrompt(websiteInfo: WebsiteInfo, config: ArticleGenerationConfig): string {
        const prompt = `
Generate a high-quality article based on the following website information:

Website Title: ${websiteInfo.title}
Website Description: ${websiteInfo.description}
Main Topics: ${websiteInfo.mainTopics.join(', ')}
Keywords to include: ${websiteInfo.keywords.join(', ')}

Additional Requirements:
- Tone: ${config.tone || 'professional'}
- Length: ${config.length || 'medium'}
- Target Audience: ${config.targetAudience || 'general'}
- Additional Keywords: ${config.keywords?.join(', ') || 'N/A'}

Please generate an article that includes:
1. An engaging title
2. Well-structured content with appropriate headings
3. A meta description for SEO (max 160 characters)
4. A brief excerpt (2-3 sentences)
5. Suggested tags for categorization

Format the response as JSON with the following structure:
{
  "title": "Article Title",
  "content": "Full article content with markdown formatting",
  "metaDescription": "SEO meta description",
  "excerpt": "Brief excerpt",
  "suggestedTags": ["tag1", "tag2", "tag3"]
}
`;

        return prompt;
    }

    public async generateArticle(
        websiteInfo: WebsiteInfo,
        config: ArticleGenerationConfig = {}
    ): Promise<GeneratedArticle> {
        try {
            const prompt = this.buildPrompt(websiteInfo, config);
            const response = await this.callGPT4(prompt);

            try {
                return JSON.parse(response) as GeneratedArticle;
            } catch (error) {
                console.error('Error parsing GPT-4 response:', error);
                throw new Error('Failed to parse generated content');
            }
        } catch (error) {
            console.error('Error generating article:', error);
            throw new Error('Failed to generate article');
        }
    }
} 