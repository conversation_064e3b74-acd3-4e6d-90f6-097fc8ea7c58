
import { Button } from '@/components/ui/button';

interface OnboardingCompleteProps {
  onComplete: () => void;
}

export const OnboardingComplete = ({ onComplete }: OnboardingCompleteProps) => {
  return (
    <div className="w-full max-w-2xl mx-auto p-8 text-center">
      <div className="mb-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">📝</span>
          </div>
          <span className="ml-3 text-2xl font-bold text-gray-800">BLOGBUSTER</span>
        </div>
        
        <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-4xl">✅</span>
        </div>
        
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          All Set! 🎉
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Your SEO blog automation is ready to go. Let's start creating amazing content!
        </p>
      </div>

      <Button
        onClick={onComplete}
        className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 text-lg"
      >
        Enter Dashboard →
      </Button>
    </div>
  );
};
