
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export const TopicsView = () => {
  const [activeTab, setActiveTab] = useState('ideas');

  const tabs = [
    { id: 'ideas', label: 'Topic ideas', count: 2 },
    { id: 'generated', label: 'Generated', count: 1 },
    { id: 'scheduled', label: 'Scheduled', count: 0 }
  ];

  const topics = [
    {
      id: 1,
      title: "How Blockchain Rewards Are Revolutionizing Online Gaming",
      keywords: "blockchain rewards, gaming with blockchain, digital token rewards, blockchain gaming benefits, earn...",
      tag: "Blockchain Rewards",
      language: "us English (United States)",
      date: "31 May 2025"
    },
    {
      id: 2,
      title: "Interactive Learning Through AI-Powered Quiz Contests",
      keywords: "interactive learning, AI-powered quizzes, gamified education, learning through games, AI quiz benefits",
      tag: "Interactive Learning", 
      language: "us English (United States)",
      date: "31 May 2025"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex border-b border-gray-200">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>

        <Button className="bg-orange-500 hover:bg-orange-600 text-white">
          💡 Get more topic
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <Button variant="outline" className="text-sm">
          Filter 2
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <div className="flex space-x-4 text-sm font-medium text-gray-600 px-4">
          <span className="w-8">☑️</span>
          <span className="flex-1">Topics list</span>
          <span className="w-24">🏷️ Tags</span>
          <span className="w-32">🌐 Language</span>
          <span className="w-24">📅 Date Created</span>
          <span className="w-16">⚡ Action</span>
        </div>

        {topics.map((topic) => (
          <Card key={topic.id} className="p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center space-x-4">
              <input type="checkbox" className="w-4 h-4" />
              
              <div className="flex-1">
                <button className="text-left">
                  <h3 className="font-medium text-gray-900 mb-1">
                    ▶ {topic.title}
                  </h3>
                  <p className="text-sm text-gray-500">
                    Keywords: {topic.keywords}
                  </p>
                </button>
              </div>
              
              <div className="w-24">
                <Badge className="bg-orange-100 text-orange-700 text-xs">
                  {topic.tag}
                </Badge>
              </div>
              
              <div className="w-32 text-sm text-gray-600">
                {topic.language}
              </div>
              
              <div className="w-24 text-sm text-gray-600">
                {topic.date}
              </div>
              
              <div className="w-16">
                <Button
                  size="sm"
                  className="bg-orange-500 hover:bg-orange-600 text-white text-xs"
                >
                  Schedule 📅
                </Button>
              </div>
              
              <button className="text-gray-400 hover:text-gray-600">
                ⋯
              </button>
            </div>
          </Card>
        ))}
      </div>

      <div className="text-center py-8">
        <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-2xl">🔒</span>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Subscribe to unlock further topics</h3>
        <Button className="bg-orange-500 hover:bg-orange-600 text-white">
          Subscribe now! 🚀
        </Button>
      </div>
    </div>
  );
};
