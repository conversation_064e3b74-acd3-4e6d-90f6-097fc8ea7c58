import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface SidebarProps {
  activeView: string;
  onViewChange: (view: string) => void;
  onWrite: () => void;
}

export const Sidebar = ({ activeView, onViewChange, onWrite }: SidebarProps) => {
  const menuItems = [
    { id: 'write', label: 'Write', icon: '✏️' },
    { id: 'articles', label: 'Article', icon: '📄' },
    { id: 'topics', label: 'Topics', icon: '💭' },
    { id: 'scheduler', label: 'Scheduler', icon: '📅' },
    { id: 'social', label: 'Social Media', icon: '📱' },
    { id: 'settings', label: 'Settings', icon: '⚙️' }
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold">📝</span>
          </div>
          <span className="font-semibold text-lg">BLOGBUSTER</span>
        </div>
      </div>

      <div className="flex-1 p-4">
        <div className="mb-8">
          <Button
            onClick={onWrite}
            className="w-full bg-orange-500 hover:bg-orange-600 text-white"
          >
            ✏️ Write
          </Button>
        </div>

        <nav className="space-y-1">
          <button
            onClick={() => onViewChange('articles')}
            className={`w-full text-left px-4 py-2 rounded-lg flex items-center gap-2 ${activeView === 'articles'
              ? 'bg-orange-50 text-orange-600'
              : 'text-gray-600 hover:bg-gray-50'
              }`}
          >
            📄 Article
          </button>
          <button
            onClick={() => onViewChange('topics')}
            className={`w-full text-left px-4 py-2 rounded-lg flex items-center gap-2 ${activeView === 'topics'
              ? 'bg-orange-50 text-orange-600'
              : 'text-gray-600 hover:bg-gray-50'
              }`}
          >
            🎯 Topics
          </button>
          <button
            onClick={() => onViewChange('scheduler')}
            className={`w-full text-left px-4 py-2 rounded-lg flex items-center gap-2 ${activeView === 'scheduler'
              ? 'bg-orange-50 text-orange-600'
              : 'text-gray-600 hover:bg-gray-50'
              }`}
          >
            📅 Scheduler
          </button>
          <button
            onClick={() => onViewChange('social')}
            className={`w-full text-left px-4 py-2 rounded-lg flex items-center gap-2 ${activeView === 'social'
              ? 'bg-orange-50 text-orange-600'
              : 'text-gray-600 hover:bg-gray-50'
              }`}
          >
            📱 Social Media
          </button>
          <button
            onClick={() => onViewChange('settings')}
            className={`w-full text-left px-4 py-2 rounded-lg flex items-center gap-2 ${activeView === 'settings'
              ? 'bg-orange-50 text-orange-600'
              : 'text-gray-600 hover:bg-gray-50'
              }`}
          >
            ⚙️ Settings
          </button>
        </nav>
      </div>

      <div className="p-4 border-t border-gray-200">
        <div className="bg-orange-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            ⚡ Trial
          </div>
          <p className="text-sm text-gray-600 mb-3">
            Upgrade to boost SEO and get organic traffic on autopilot
          </p>
          <Button
            variant="outline"
            className="w-full border-orange-500 text-orange-500 hover:bg-orange-50"
          >
            Upgrade Plan ⚡
          </Button>
        </div>

        <div className="mt-4 space-y-2 text-sm text-gray-600">
          <button className="flex items-center w-full px-2 py-1 hover:bg-gray-50 rounded">
            <span className="mr-2">🤖</span>
            WinzyAI
          </button>
          <button className="flex items-center w-full px-2 py-1 hover:bg-gray-50 rounded">
            <span className="mr-2">💬</span>
            Support
          </button>
          <button className="flex items-center w-full px-2 py-1 hover:bg-gray-50 rounded">
            <span className="mr-2">📝</span>
            Feedback
          </button>
        </div>
      </div>

    </div>
  );
};
